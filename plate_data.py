import requests
import pandas as pd

# 获取概念板块数据
def get_concept_plate_data():
    url = 'https://dq.10jqka.com.cn/fuyao/hot_list_data/out/hot_list/v1/plate?type=concept'
    response = requests.get(url)
    if response.status_code == 200:
        data = response.json()
        df = pd.DataFrame(data['data'])
        return df
    else:
        print(f"获取概念板块数据失败，状态码: {response.status_code}")
        return None

# 获取行业板块数据
def get_industry_plate_data():
    url = 'https://dq.10jqka.com.cn/fuyao/hot_list_data/out/hot_list/v1/plate?type=industry'
    response = requests.get(url)
    if response.status_code == 200:
        data = response.json()
        df = pd.DataFrame(data['data'])
        return df
    else:
        print(f"获取行业板块数据失败，状态码: {response.status_code}")
        return None

# 示例用法
if __name__ == '__main__':
    concept_data = get_concept_plate_data()
    if concept_data is not None:
        print("概念板块数据:")
        print(concept_data.head())
    
    industry_data = get_industry_plate_data()
    if industry_data is not None:
        print("\n行业板块数据:")
        print(industry_data.head())