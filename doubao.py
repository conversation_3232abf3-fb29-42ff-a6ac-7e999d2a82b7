import argparse
import logging
from datetime import datetime

import pandas as pd
import psycopg2
from openpyxl import Workbook
from openpyxl.styles import <PERSON><PERSON><PERSON><PERSON>, Alignment, Font
from openpyxl.utils import get_column_letter
from psycopg2 import sql

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("backtest.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class StockStrategyBacktest:
    def __init__(self, db_params):
        """初始化数据库连接参数和结果存储"""
        self.db_params = db_params
        self.strategy_results = {}
        self.conn = None

    def connect_db(self):
        """建立数据库连接"""
        try:
            self.conn = psycopg2.connect(**self.db_params)
            logger.info("成功连接到数据库")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False

    def close_db(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            logger.info("数据库连接已关闭")

    def fetch_stock_data(self, start_date, end_date):
        """从数据库获取股票数据"""
        if not self.conn:
            if not self.connect_db():
                return None

        try:
            with self.conn.cursor() as cursor:
                query = sql.SQL("""
                                SELECT ts_code,
                                       trade_date, open, high, low, close, pre_close, change_price, pct_chg, vol, amount
                                FROM a_stock_history
                                WHERE trade_date BETWEEN %s
                                  AND %s
                                ORDER BY ts_code, trade_date
                                """)
                cursor.execute(query, (start_date, end_date))
                columns = [desc[0] for desc in cursor.description]
                data = cursor.fetchall()
                df = pd.DataFrame(data, columns=columns)
                logger.info(f"成功获取 {len(df)} 条股票数据")
                return df
        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return None

    def calculate_returns(self, stock_data, holding_periods=[1, 3, 5, 10, 20]):
        """计算不同持有期的收益率"""
        if stock_data is None or stock_data.empty:
            logger.warning("没有数据用于计算收益率")
            return None

        # 按股票代码分组计算收益率
        grouped = stock_data.groupby('ts_code')
        all_returns = []

        for ts_code, group in grouped:
            # 确保数据按日期排序
            group = group.sort_values('trade_date')

            # 计算未来不同持有期的收益率
            for period in holding_periods:
                group[f'return_{period}d'] = group['close'].pct_change(period).shift(-period)

            # 重置索引并添加到结果列表
            all_returns.append(group)

        # 合并所有结果
        result_df = pd.concat(all_returns)
        logger.info(f"收益率计算完成，涵盖 {len(result_df['ts_code'].unique())} 只股票")
        return result_df

    def apply_strategy(self, stock_data, strategy_func, strategy_name="策略"):
        """应用交易策略并评估结果"""
        if stock_data is None or stock_data.empty:
            logger.warning("没有数据用于策略评估")
            return None

        # 应用策略，生成买入信号
        strategy_data = strategy_func(stock_data.copy())
        buy_signals = strategy_data[strategy_data['buy_signal'] == 1].copy()

        if buy_signals.empty:
            logger.warning(f"{strategy_name} 没有生成买入信号")
            return None

        # 计算不同持有期的表现
        holding_periods = [1, 3, 5, 10, 20]
        results = {'买入次数': len(buy_signals)}

        for period in holding_periods:
            return_col = f'return_{period}d'
            if return_col in buy_signals.columns:
                # 计算上涨概率
                pos_returns = buy_signals[buy_signals[return_col] > 0]
                win_prob = len(pos_returns) / len(buy_signals) * 100

                # 计算平均收益率（仅考虑有数据的）
                valid_returns = buy_signals[return_col].dropna()
                avg_return = valid_returns.mean() * 100

                results[f'{period}日上涨概率(%)'] = win_prob
                results[f'{period}日平均收益率(%)'] = avg_return

        self.strategy_results[strategy_name] = results
        logger.info(f"{strategy_name} 回测完成，买入信号数量: {len(buy_signals)}")
        return strategy_data, buy_signals

    def plot_strategy_results(self, strategy_name="策略"):
        """可视化策略回测结果 (已禁用图片生成)"""
        if strategy_name not in self.strategy_results:
            logger.warning(f"没有找到 {strategy_name} 的回测结果")
            return

        # 不再生成PNG图片
        logger.info(f"已禁用图表生成功能")

    def generate_report(self, output_format='excel', output_file='strategy_report', start_date='', end_date=''):
        """生成回测报告"""
        if not self.strategy_results:
            logger.warning("没有策略回测结果可用于生成报告")
            return False

        # 为每个策略生成单独的Excel文件
        for strategy_name, results in self.strategy_results.items():
            # 创建符合模板格式的DataFrame
            # 创建基本结构
            columns = ['策略名称', '策略条件描述', '回测日期范围', '买入次数', '表现', '1 日', '3 日', '5 日', '10 日', '20 日']

            # 根据策略名称生成策略描述
            strategy_description = ''
            date_range = f'{start_date} 至 {end_date}'

            if strategy_name == '均线交叉策略':
                strategy_description = '5日均线上穿20日均线时产生买入信号'
            elif strategy_name == '成交量突破策略':
                strategy_description = '成交量超过20日均值2倍标准差时产生买入信号'
            elif strategy_name == '神奇九转策略':
                strategy_description = '连续9天收盘价低于4天前收盘价且第8或9天最低价小于第6或7天最低价时产生买入信号'
            elif strategy_name == 'N字反包策略':
                strategy_description = '寻找N字反包形态，即回调后的收盘价低于前一个上涨波段的高点，且当前收盘价高于前一个上涨波段的高点时产生买入信号'
            elif strategy_name == '涨停回调策略':
                strategy_description = '股票涨停后，后续回调不跌破涨停板的最低价时产生买入信号'

            data = [
                [strategy_name, strategy_description, date_range, results['买入次数'], '上涨概率',
                 results.get('1日上涨概率(%)', ''),
                 results.get('3日上涨概率(%)', ''),
                 results.get('5日上涨概率(%)', ''),
                 results.get('10日上涨概率(%)', ''),
                 results.get('20日上涨概率(%)', '')],
                ['', '', '', '', '平均收益率',
                 results.get('1日平均收益率(%)', ''),
                 results.get('3日平均收益率(%)', ''),
                 results.get('5日平均收益率(%)', ''),
                 results.get('10日平均收益率(%)', ''),
                 results.get('20日平均收益率(%)', '')]
            ]

            # 生成文件名
            file_name = f'{strategy_name}-策略表现结果.xlsx'

            # 创建工作簿和工作表
            wb = Workbook()
            ws = wb.active
            ws.title = "策略表现"

            # 添加表头和数据
            # 先添加列标题
            for col_idx, col_name in enumerate(columns, 1):
                cell = ws.cell(row=1, column=col_idx, value=col_name)
                # 设置表头底色为浅蓝色
                cell.fill = PatternFill(start_color="B8CCE4", end_color="B8CCE4", fill_type="solid")
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center', vertical='center')

            # 添加数据行
            for row_idx, row_data in enumerate(data, 2):
                for col_idx, value in enumerate(row_data, 1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    cell.alignment = Alignment(horizontal='center', vertical='center')

            # 合并前四列的第二行和第三行单元格
            for col_idx in range(1, 5):  # 前四列：策略名称、策略条件描述、回测日期范围、买入次数
                # 合并第二行和第三行
                ws.merge_cells(start_row=2, start_column=col_idx, end_row=3, end_column=col_idx)
                # 设置合并后单元格的对齐方式
                merged_cell = ws.cell(row=2, column=col_idx)
                merged_cell.alignment = Alignment(horizontal='center', vertical='center')

            # 设置固定列宽和行高
            for col_idx in range(1, len(columns) + 1):
                column = get_column_letter(col_idx)
                # 策略条件描述列宽为48，其他列宽为24
                if col_idx == 2:  # 策略条件描述列
                    ws.column_dimensions[column].width = 48
                else:
                    ws.column_dimensions[column].width = 24

            # 设置行高为64
            for row_idx in range(1, 4):
                ws.row_dimensions[row_idx].height = 64

            # 为所有单元格添加框线
            from openpyxl.styles import Border, Side
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            for row_idx in range(1, 4):
                for col_idx in range(1, len(columns) + 1):
                    ws.cell(row=row_idx, column=col_idx).border = thin_border

            # 保存Excel文件，处理文件可能被占用的情况
            try:
                wb.save(file_name)
                logger.info(f"策略 '{strategy_name}' 的报告已保存为: {file_name}")
            except PermissionError:
                # 如果文件被占用，尝试使用不同的文件名
                alt_file_name = f'{strategy_name}-策略表现结果_{datetime.now().strftime("%H%M%S")}.xlsx'
                try:
                    wb.save(alt_file_name)
                    logger.info(f"原文件被占用，策略 '{strategy_name}' 的报告已保存为: {alt_file_name}")
                except Exception as e:
                    logger.error(f"保存策略 '{strategy_name}' 的报告时出错: {e}")
            except Exception as e:
                logger.error(f"保存策略 '{strategy_name}' 的报告时出错: {e}")

        # 打印报告摘要
        print("\n策略回测报告摘要:")
        for strategy_name, results in self.strategy_results.items():
            print(f"\n策略: {strategy_name}")
            for key, value in results.items():
                print(f"{key}: {value:.2f}" if isinstance(value, (int, float)) else f"{key}: {value}")

        return True


# 定义示例交易策略
def ma_crossover_strategy(df):
    """简单移动平均线交叉策略"""
    # 按股票代码分组处理
    grouped = df.groupby('ts_code')
    results = []

    for ts_code, group in grouped:
        # 确保数据按日期排序
        group = group.sort_values('trade_date')

        # 计算短期和长期移动平均线
        group['ma5'] = group['close'].rolling(window=5).mean()
        group['ma20'] = group['close'].rolling(window=20).mean()

        # 生成买入信号 (5日均线上穿20日均线)
        group['buy_signal'] = 0
        group.loc[(group['ma5'] > group['ma20']) &
                  (group['ma5'].shift(1) <= group['ma20'].shift(1)), 'buy_signal'] = 1

        results.append(group)

    return pd.concat(results)


def volume_breakout_strategy(df):
    """成交量突破策略"""
    # 按股票代码分组处理
    grouped = df.groupby('ts_code')
    results = []

    for ts_code, group in grouped:
        # 确保数据按日期排序
        group = group.sort_values('trade_date')

        # 计算成交量均值和标准差
        group['vol_mean'] = group['vol'].rolling(window=20).mean()
        group['vol_std'] = group['vol'].rolling(window=20).std()

        # 生成买入信号 (成交量突破2倍标准差)
        group['buy_signal'] = 0
        group.loc[group['vol'] > group['vol_mean'] + 2 * group['vol_std'], 'buy_signal'] = 1

        results.append(group)

    return pd.concat(results)


def magic_nine_turns_strategy(df):
    """神奇九转买入策略
    连续9天收盘价低于4天前的收盘价，形成低九买入结构
    同时要求第8或9天的最低价小于第6或7天的最低价
    """
    # 按股票代码分组处理
    grouped = df.groupby('ts_code')
    results = []

    for ts_code, group in grouped:
        # 确保数据按日期排序
        group = group.sort_values('trade_date')

        # 初始化买入信号列
        group['buy_signal'] = 0

        # 计算每天的收盘价是否低于4天前的收盘价
        group['lower_than_4days_ago'] = (group['close'] < group['close'].shift(4)).astype(int)

        # 初始化计数器列
        group['count'] = 0

        # 计算连续满足条件的天数
        count = 0
        for i in range(len(group)):
            if group['lower_than_4days_ago'].iloc[i] == 1:
                count += 1
                group.iloc[i, group.columns.get_loc('count')] = count
            else:
                count = 0
                group.iloc[i, group.columns.get_loc('count')] = 0

        # 找出连续9天满足条件的点
        for i in range(8, len(group)):
            # 检查是否有连续9天收盘价低于4天前
            if group['count'].iloc[i] >= 9:
                # 检查第8或9天的最低价是否小于第6或7天的最低价
                day8_low = group['low'].iloc[i-1]
                day9_low = group['low'].iloc[i]
                day6_low = group['low'].iloc[i-3]
                day7_low = group['low'].iloc[i-2]

                if (day8_low < day6_low or day8_low < day7_low or
                    day9_low < day6_low or day9_low < day7_low):
                    # 满足条件，生成买入信号
                    group.iloc[i, group.columns.get_loc('buy_signal')] = 1

        results.append(group)

    return pd.concat(results)

# 定义N字反包交易策略
def n_shape_reversal_strategy(df):
    """N字反包交易策略"""
    # 按股票代码分组处理
    grouped = df.groupby('ts_code')
    results = []

    for ts_code, group in grouped:
        # 确保数据按日期排序
        group = group.sort_values('trade_date')

        # 初始化买入信号列
        group['buy_signal'] = 0

        # 遍历数据，寻找N字反包形态
        for i in range(2, len(group)):
            # 前一个上涨波段的高点
            prev_high = group['high'].iloc[i - 2]
            # 回调后的收盘价
            callback_close = group['close'].iloc[i - 1]
            # 当前收盘价
            current_close = group['close'].iloc[i]

            # 判断是否满足N字反包条件
            if callback_close < prev_high and current_close > prev_high:
                group.at[group.index[i], 'buy_signal'] = 1

        results.append(group)

    return pd.concat(results)

# 定义涨停回调策略
def limit_up_callback_strategy(df):
    """涨停回调策略：股票涨停后，后续回调不跌破涨停板的最低价"""
    # 按股票代码分组处理
    grouped = df.groupby('ts_code')
    results = []

    for ts_code, group in grouped:
        # 确保数据按日期排序
        group = group.sort_values('trade_date')

        # 初始化买入信号列
        group['buy_signal'] = 0

        # 标记涨停板
        group['limit_up'] = (group['pct_chg'] >= 9.5).astype(int)

        limit_up_index = None
        limit_up_low = None

        for i in range(len(group)):
            if group['limit_up'].iloc[i] == 1:
                limit_up_index = i
                limit_up_low = group['low'].iloc[i]
            elif limit_up_index is not None:
                if group['low'].iloc[i] >= limit_up_low:
                    group.at[group.index[i], 'buy_signal'] = 1
                else:
                    limit_up_index = None
                    limit_up_low = None

        results.append(group)

    return pd.concat(results)

# 定义连续平稳后涨停策略
def continuous_stable_then_limit_up_strategy(df):
    """连续10个交易日的收盘价涨跌幅度在5%以内，接着第11个交易日涨停买入"""
    grouped = df.groupby('ts_code')
    results = []

    for ts_code, group in grouped:
        group = group.sort_values('trade_date')
        group['buy_signal'] = 0

        for i in range(10, len(group)):
            # 检查连续10个交易日的收盘价涨跌幅度在5%以内
            stable_condition = all(abs(group['pct_chg'].iloc[i-10:i]) <= 5)
            # 检查第11个交易日是否涨停
            limit_up_condition = group['pct_chg'].iloc[i] >= 9.5

            if stable_condition and limit_up_condition:
                group.at[group.index[i], 'buy_signal'] = 1

        results.append(group)

    return pd.concat(results)

# 定义价格突破策略
def price_breakout_strategy(df):
    """价格突破策略：价格突破过去20日最高价时买入"""
    # 按股票代码分组处理
    grouped = df.groupby('ts_code')
    results = []

    for ts_code, group in grouped:
        # 确保数据按日期排序
        group = group.sort_values('trade_date')

        # 计算20日最高价
        group['high_20d'] = group['high'].rolling(window=20).max()

        # 生成买入信号：当日收盘价突破20日最高价
        group['buy_signal'] = 0
        group.loc[group['close'] > group['high_20d'].shift(1), 'buy_signal'] = 1

        results.append(group)

    return pd.concat(results)

# deepseek生成的
# 找出近13个交易日内有涨停的股票，然后再排除3连板及以上的股票，最后计算月线出现MACD金叉以及股价回落至日线的20日均线附近放量上涨离前高5%或出现涨停时买入
def macd_ma_volume_strategy(df):
    """优化后的MACD月线金叉+20日线策略"""
    grouped = df.groupby('ts_code')
    results = []

    for ts_code, group in grouped:
        group = group.sort_values('trade_date')
        group['buy_signal'] = 0
        group['trade_date'] = pd.to_datetime(group['trade_date'])

        # === 数据预处理 ===
        # 1. 标记涨停日
        group['limit_up'] = (group['pct_chg'] >= 9.5).astype(int)

        # 2. 计算13日内涨停次数（滚动窗口包含当日）
        group['limit_up_13d'] = group['limit_up'].rolling(13, min_periods=1).sum().shift(1)  # 使用前13日数据

        # 3. 排除3连板及以上（使用严格连续判断）
        group['consecutive_limit'] = (
                group['limit_up']
                .groupby((group['limit_up'].diff() != 0).cumsum())
                .cumcount() + 1
        )
        invalid_mask = group['consecutive_limit'] >= 3

        # === 月线MACD计算 ===
        # === 修复2：重新计算月线MACD ===
        # 生成月线数据（必须设置datetime索引）
        monthly = (
            group.set_index('trade_date')
            .resample('M')['close']
            .last()  # 取每月最后一个交易日
            .to_frame(name='close')
            .reset_index()
        )
        # 计算月线MACD（确保数据长度足够）
        if len(monthly) >= 26:
            monthly['DIF'] = monthly['close'].ewm(span=12, adjust=False).mean() - monthly['close'].ewm(span=26,
                                                                                                       adjust=False).mean()
            monthly['DEA'] = monthly['DIF'].ewm(span=9, adjust=False).mean()
            monthly['macd_golden'] = (monthly['DIF'] > monthly['DEA']) & (
                        monthly['DIF'].shift() <= monthly['DEA'].shift())
        else:
            monthly['macd_golden'] = False  # 数据不足时跳过

        # 合并月线金叉信号到日线
        # === 修复3：正确合并月线信号 ===
        # 将月线信号合并到日线（使用merge_asof按最近日期匹配）
        group = pd.merge_asof(
            group.sort_values('trade_date'),
            monthly[['trade_date', 'macd_golden']].sort_values('trade_date'),
            on='trade_date',
            direction='backward',
            suffixes=('', '_month')
        )

        # === 日线指标计算 ===
        # 4. 计算20日均线（前复权）
        group['close'] = group['close'].astype(float)
        group['ma20'] = group['close'].rolling(20, min_periods=1).mean()
        group['ma20'] = group['ma20'].astype(float)
        # 5. 计算前20日最高价（不包含当日）
        group['prev_20d_high'] = group['high'].shift(1).rolling(20, min_periods=1).max()

        # === 条件判断 ===
        for i in range(20, len(group)):  # 确保所有滚动窗口有效
            # 基础条件
            cond_macd = group['macd_golden'].iloc[i]  # 月线MACD金叉
            cond_limit = group['limit_up_13d'].iloc[i] >= 1  # 前13日有涨停
            cond_exclude = not invalid_mask.iloc[i]  # 排除3连板

            # 价格条件（动态容差）
            current_close = group['close'].iloc[i]
            ma20 = group['ma20'].iloc[i]
            price_diff = abs(float(current_close) - float(ma20)) / float(ma20)

            cond_ma = price_diff <= 0.02  # 允许±2%波动

            # 量价条件
            vol_mean = group['vol'].iloc[i - 20:i].mean()  # 前20日平均成交量（不包含当日）
            cond_vol = group['vol'].iloc[i] > vol_mean * 1.5  # 放量1.5倍
            cond_high = (current_close >= group['prev_20d_high'].iloc[i] * 0.95) or (group['limit_up'].iloc[i] == 1)

            # 综合信号
            if cond_macd and cond_limit and cond_exclude and cond_ma and cond_vol and cond_high:
                group.at[group.index[i], 'buy_signal'] = 1

        results.append(group)

    return pd.concat(results)

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='A股交易策略回测分析工具')
    parser.add_argument('--host', default='localhost', help='数据库主机地址')
    parser.add_argument('--port', default=5432, type=int, help='数据库端口')
    parser.add_argument('--dbname', default='stock_data', help='数据库名称')
    parser.add_argument('--user', default='root', help='数据库用户名')
    parser.add_argument('--password', default='password', help='数据库密码')
    parser.add_argument('--start_date', default='2005-01-01', help='回测开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end_date', default=datetime.now().strftime('%Y-%m-%d'),
                        help='回测结束日期 (YYYY-MM-DD)')
    parser.add_argument('--output_format', default='excel', choices=['csv', 'excel'],
                        help='报告输出格式')
    parser.add_argument('--output_file', default='strategy_report', help='报告输出文件名')

    args = parser.parse_args()

    # 数据库连接参数
    db_params = {
        'host': '127.0.0.1',
        'port': 5432,
        'dbname': 'tushare',
        'user': 'root',
        'password': '123629He'
    }

    # 创建回测实例
    backtest = StockStrategyBacktest(db_params)

    # 获取股票数据
    stock_data = backtest.fetch_stock_data(args.start_date, args.end_date)
    if stock_data is None or stock_data.empty:
        logger.error("没有获取到股票数据，程序退出")
        return

    # 计算收益率
    return_data = backtest.calculate_returns(stock_data)
    if return_data is None:
        logger.error("收益率计算失败，程序退出")
        return

    # 应用策略并评估
    strategies = {
        # '均线交叉策略': ma_crossover_strategy,
        # '成交量突破策略': volume_breakout_strategy,
        # '神奇九转策略': magic_nine_turns_strategy,
        # 'N字反包策略': n_shape_reversal_strategy,
        # '涨停回调策略': limit_up_callback_strategy,
        # '价格突破策略': price_breakout_strategy,
        # '连续平稳后涨停策略': continuous_stable_then_limit_up_strategy,
        'MACD月线金叉+20日线附近放量突破策略': macd_ma_volume_strategy
    }

    for name, strategy in strategies.items():
        _, signals = backtest.apply_strategy(return_data, strategy, name)
        if signals is not None:
            # 不再生成图表
            pass

    # 生成报告 (始终使用Excel格式)
    backtest.generate_report('excel', args.output_file, args.start_date, args.end_date)

    # 关闭数据库连接
    backtest.close_db()


if __name__ == "__main__":
    main()